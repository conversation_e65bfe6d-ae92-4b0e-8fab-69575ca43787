from flask import jsonify, request
from core.api import api_bp
from core.utils.auth_utils import auth_required
import validators


@api_bp.route('/hello', methods=['GET'])
@auth_required
def hello():
    return jsonify({'hello': 'world'})


@api_bp.route('/organization-data', methods=['POST'])
@auth_required
def process_url_post():
    """
    Accept URL in POST request body
    Example: POST /api/process-url with JSON body: {"url": "https://example.com"}
    """
    data = request.get_json()

    if not data or 'url' not in data:
        return jsonify({'error': 'URL is required in request body'}), 400

    url = data['url']

    # Validate URL format
    if not validators.url(url):
        return jsonify({'error': 'Invalid URL format'}), 400
    
    ai model

    # Process the URL here (your business logic)
    result = {
        'message': 'URL processed successfully',
        'url': url,
        'method': 'POST',
        'additional_data': {k: v for k, v in data.items() if k != 'url'}
    }

    return jsonify(result)



